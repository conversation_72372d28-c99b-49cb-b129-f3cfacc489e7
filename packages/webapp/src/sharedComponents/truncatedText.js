import React, { useState } from 'react';

const TruncatedHTML = ({ text, maxLength = 321, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) return null;
  const plainText = text
    // paragraph gap
    .replace(/<\/p>\s*<p>/gi, '\n\n') // paragraph gap
    .replace(/<p>/gi, '')             // opening <p> remove
    .replace(/<\/p>/gi, '')           // closing </p> remove

    // <br> convert this to new line
    .replace(/<br\s*\/?>/gi, '\n')

    // remove all other HTML tags
    .replace(/<[^>]+>/g, '');

  const shouldShowMore = plainText.length > maxLength;
  const displayHTML = isExpanded
    ? plainText
    : plainText.slice(0, maxLength) + (plainText.length > maxLength ? '' : '');

  const toggleExpanded = () => setIsExpanded(!isExpanded);

  return (
    <p className={className}>
      {displayHTML}
      {shouldShowMore && (
        <span
          style={{ cursor: 'pointer', color: '#1743d7' }}
          onClick={toggleExpanded}
          className={className}
        >
          {isExpanded ? ' less' : ' more...'}
        </span>
      )}
    </p>
  );
};

export default TruncatedHTML;
