import React from 'react';
import { useRouter } from 'next/router';
import Button from 'sharedComponents/Button/button';
import { get } from 'lodash';
import { isRequiredFieldPresent, decodeHtmlEntities } from 'utils/helper';
import TruncatedText from 'sharedComponents/truncatedText';
import style from './styles/calloutSubmissionSidebar.module.scss';

const CalloutSubmissionSidebar = ({
  selectedSnapId,
  callout,
  onSubmit,
  isCalloutView,
}) => {
  const { pathname } = useRouter();
  const isCalloutDetailsPage = pathname === '/callouts/[id]';

  const isRequiredField = get(callout, 'requiredFields', {});
  const validateRequiredFields = isRequiredFieldPresent(isRequiredField);

  return (
    <div className={`pl-md-4 ${style.sidebarContainer}`}>
      {/* Submit Button */}
      <div>
        <Button
          btntype="submit"
          id="submitbutton"
          size="medium"
          className="w-100"
          buttonValue={selectedSnapId ? 'SUBMIT TO CALL OUT' : 'SELECT PROJECT'}
          isActive={selectedSnapId}
          customClass={
            selectedSnapId ? 'submitToCalloutBtn' : 'disableSubmitToCalloutBtn'
          }
          clickHandler={selectedSnapId ? onSubmit : undefined}
        />
      </div>

      {/* Call Out Required Fields Section */}
      {validateRequiredFields && (
        <div className="py-5">
          <h4 className="mb-3">Required to submit</h4>
          <ul
            className={`px-3 mb-0 list-none max-h-64 overflow-y-auto ${style.requiredFieldList}`}
          >
            {Object.entries(isRequiredField)
              .flatMap(([sectionKey, sectionValue]) => {
                if (typeof sectionValue === 'object') {
                  return Object.entries(sectionValue)
                    .filter(([, value]) => value === true)
                    .map(([key]) =>
                      key
                        .replace(/([A-Z])/g, ' $1')
                        .replace(/^./, (str) => str.toUpperCase()),
                    );
                }

                if (sectionValue === true) {
                  return sectionKey
                    .replace(/([A-Z])/g, ' $1')
                    .replace(/^./, (str) => str.toUpperCase());
                }

                return [];
              })
              .sort()
              .map((item, index) => (
                <li key={index} className="mb-2 capitalize break-inside-avoid">
                  {item}
                </li>
              ))}
          </ul>
        </div>
      )}

      {/* Call Out Summary Section */}
      {get(callout, 'body.companyProfile') &&
        !isCalloutDetailsPage &&
        !isCalloutView && (
          <div className={`${!validateRequiredFields ? 'pt-5' : 'pb-5 '}`}>
            <h4 className="mb-3">CALL OUT SUMMARY</h4>
            <TruncatedText
              text={decodeHtmlEntities(get(callout, 'body.content'))}
              maxLength={321}
              className="mb-0"
            />
          </div>
        )}

      {/* About the Discoverer Section */}
      {get(callout, 'body.content') && (
        <div className={`mb-4 ${!validateRequiredFields ? 'pt-5' : ''}`}>
          <h4 className="mb-3">ABOUT THE DISCOVERER</h4>
          <TruncatedText
            text={decodeHtmlEntities(get(callout, 'body.companyProfile'))}
            maxLength={321}
            className="mb-3"
          />
        </div>
      )}
    </div>
  );
};

export default CalloutSubmissionSidebar;
