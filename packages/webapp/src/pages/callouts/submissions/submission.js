import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import { useSelector } from 'react-redux';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';
import SubmissionCard from 'sharedComponents/SubmissionCard/submissionCard';
import Button from 'sharedComponents/Button/button';
import Loader from 'sharedComponents/loader';
import CustomCarousel from 'sharedComponents/CustomCaraousel/carousel';

const Submissions = ({ onViewMore, onBack, expandSubmissions }) => {
  const calloutSubmissions = useSelector(
    (state) => state.callout.calloutSubmissions,
  );
  const isLoading = useSelector((state) => state.callout.isLoading);
  const currentUserId = useSelector((state) => get(state, 'auth.userData._id'));

  const [userSubmissions, setUserSubmissions] = useState([]);
  const [shouldShowAllSubmissions, setShouldShowAllSubmissions] = useState(
    expandSubmissions || false,
  );

  useEffect(() => {
    if (expandSubmissions && !shouldShowAllSubmissions) {
      setShouldShowAllSubmissions(true);
      onViewMore?.();
    }
  }, [expandSubmissions, onViewMore, shouldShowAllSubmissions]);

  useEffect(() => {
    const submissionsArray = get(
      calloutSubmissions,
      'docs',
      calloutSubmissions,
    );

    if (!Array.isArray(submissionsArray) || submissionsArray.length === 0) {
      setUserSubmissions([]);
      return;
    }

    const filtered = submissionsArray.filter((submission) => {
      const creatorUserId =
        get(submission, 'projectCreator.userId', '') ||
        get(submission, 'submissions.projectCreator.userId');

      return creatorUserId === currentUserId;
    });

    setUserSubmissions(filtered);
  }, [calloutSubmissions, currentUserId]);

  const handleViewMoreSubmissions = () => {
    setShouldShowAllSubmissions(true);
    onViewMore?.();
  };

  const handleNavigateBack = () => {
    setShouldShowAllSubmissions(false);
    onBack?.();
  };

  const visibleSubmissions = userSubmissions;
  const hasAdditionalSubmissions = visibleSubmissions.length > 3;

  return (
    <div className="col-12 mt-0">
      <SectionHeader
        title="MY CALL OUT SUBMISSIONS"
        showButton={
          !shouldShowAllSubmissions && hasAdditionalSubmissions && !isLoading
        }
        buttonText="View All"
        customClass="viewListBtn"
        alignment={shouldShowAllSubmissions ? 'center' : 'left'}
        showBackButton={shouldShowAllSubmissions}
        handleBackBtn={handleNavigateBack}
        onButtonClick={handleViewMoreSubmissions}
        headerTitleClass="fs-20"
      />

      {isLoading ? (
        <div className="text-center py-4">
          <Loader />
          <p className="mt-3">Loading all submissions...</p>
        </div>
      ) : visibleSubmissions.length > 0 ? (
        <div className="pt-3 pb-5 border-bottom">
          {!shouldShowAllSubmissions ? (
            <CustomCarousel
              items={visibleSubmissions.slice(0, 9)}
              renderItem={(submission) => (
                <SubmissionCard submission={submission} key={submission._id} />
              )}
              slidesPerPage={3}
              autoplay={false}
            />
          ) : (
            <div className="d-flex flex-wrap" style={{ gap: '16px' }}>
              {visibleSubmissions.map((submission, index) => (
                <div
                  key={submission._id || index}
                  className="submission-card-wrapper"
                  style={{
                    flex: '0 0 100%',
                    maxWidth: '100%',
                    marginBottom: '16px',
                  }}
                >
                  <style jsx>{`
                    @media (min-width: 768px) {
                      .submission-card-wrapper {
                        flex: 0 0 calc(33.333% - 11px) !important;
                        max-width: calc(33.333% - 11px) !important;
                        margin-bottom: 0 !important;
                      }
                    }
                  `}</style>
                  <SubmissionCard submission={submission} />
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-4 w-100 border-bottom">
          <p className="mb-0">No submissions found.</p>
        </div>
      )}

      {!shouldShowAllSubmissions && hasAdditionalSubmissions && (
        <div className="d-flex mt-0 mt-md-5 mt-lg-5 d-md-none d-lg-none justify-content-center pb-32 border-bottom">
          <Button
            btntype="button"
            customClass="viewListBtn"
            className="w-100 py-2 px-3"
            buttonValue="View All"
            clickHandler={handleViewMoreSubmissions}
          />
        </div>
      )}
    </div>
  );
};

export default Submissions;
