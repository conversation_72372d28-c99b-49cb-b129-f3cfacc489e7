import React from 'react';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import Router from 'next/router';
import { get } from 'lodash';
import { connect } from 'react-redux';
import Style from '../styles/startApp.module.scss';
import ProjectMenu from './ProjectMenu';
import { setSubscriptionJourneyModal } from 'reducer/subscription';

// Project cover data show form component
class Cover extends React.PureComponent {
  handleOpenProject = (item) => {
    const {
      setSubscriptionJourneyModal,
      isProjectLocked,
      isCollaborationLocked,
      type,
    } = this.props;
    if (type !== 'my') {
      // For collaborating projects, lock if not allowed or expired
      if (isCollaborationLocked) {
        setSubscriptionJourneyModal({
          modalStatus: true,
          cameFrom: 'projects',
          onComplete: () => this.handleOpenProject(item),
          feature: 'projectCollaborators',
        });
      } else {
        setSubscriptionJourneyModal({
          modalStatus: false,
          feature: '',
          cameFrom: 'projects',
          onComplete: () => {},
        });
        Router.push(
          '/project/overview/[homePage]',
          `/project/overview/${item._id}`,
        );
      }
    } else {
      // For existing projects, use the original logic
      if (isProjectLocked) {
        setSubscriptionJourneyModal({
          modalStatus: true,
          cameFrom: 'projects',
          onComplete: () => this.handleOpenProject(item),
          feature: 'unlimitedProjects',
        });
      } else {
        setSubscriptionJourneyModal({
          modalStatus: false,
          feature: '',
          cameFrom: 'projects',
          onComplete: () => {},
        });
        Router.push(
          '/project/overview/[homePage]',
          `/project/overview/${item._id}`,
        );
      }
    }
  };
  render() {
    const props = this.props;
    const {
      t,
      cover,
      item,
      callout,
      accessibleFeature,
      type,
      collaboratorList,
      canCreateProject,
      isLocked,
      isOpen,
      onMenuToggle,
      isCollaborationLocked,
      isExistingContainer = true,
    } = props;

    // Menu option flags

    const actions =
      type === 'collaboration'
        ? ['EDIT', 'SNAPSHOTS', 'COLLABORATORS', 'STOP COLLABORATING']
        : ['EDIT', 'SNAPSHOTS', 'COLLABORATORS', 'DELETE'];

    const showBadges =
      type === 'collaboration' ||
      (type === 'my' &&
        Array.isArray(item.projectCollaborator) &&
        item.projectCollaborator.length > 0);

    return (
      <div
        className={`
        ${isExistingContainer ? Style.existingAppContainer : 'position-relative'}
        ${showBadges ? 'pt-5 px-5 px-md-5 mb-0 pb-md-0' : 'pt-5 p-5 p-md-5'}
      `}
      >
        <div
          className={`${Style.existingAppSubContainer} ${isExistingContainer ? '' : ' py-3 py-md-30 py-lg-30'}`}
        >
          <div
            className={`${Style.container}`}
            onClick={callout ? null : () => this.handleOpenProject(item)}
          >
            <div className="row m-0 p-0 justify-content-center">
              <p data-cy="producerTitle" className={`${Style.headers}`}>
                {t('common:projectCreate.preview.producedBy')}
              </p>
              <p
                data-cy="producerName"
                className={`${Style.subHeader}`}
                style={{ maxWidth: '225px' }}
              >
                {get(cover, 'producer') && cover.producer}
              </p>
            </div>
            <div className={`${Style.hr}`} />
            <div className="container">
              <div className="row">
                <div className={`${Style.titleContainer} col-12 text-center`}>
                  <p data-cy="titleText" className={`${Style.title}`}>
                    {get(cover, 'title') && cover.title}
                  </p>
                </div>
              </div>
            </div>
            <div className={`${Style.hr}`} />
            <div className="container">
              <div className="row">
                <div className="col">
                  <div className={`${Style.director} row`}>
                    <p data-cy="directorTitle" className={`${Style.headers}`}>
                      {t('common:projectCreate.preview.directedBy')}
                    </p>
                    <p data-cy="directorName" className={`${Style.subHeader}`}>
                      {get(cover, 'director') && cover.director}
                    </p>
                  </div>
                </div>
                <div className={`${Style.vl}`} />
                <div className="col">
                  <div className={`${Style.writer} row`}>
                    <p data-cy="writerTitle" className={`${Style.headers}`}>
                      {t('common:projectCreate.preview.writtenBy')}
                    </p>
                    <p data-cy="writerName" className={`${Style.subHeader}`}>
                      {get(cover, 'writer') && cover.writer}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            {/* ProjectMenu for actions */}
            <ProjectMenu
              canCreateProject={canCreateProject}
              isCollaboratingProject={type === 'collaboration'}
              item={item}
              accessibleFeature={accessibleFeature}
              isCollaborationLocked={isCollaborationLocked}
              isOpen={isOpen}
              onMenuToggle={onMenuToggle}
              actions={actions}
              collaboratorList={collaboratorList}
              isLocked={isLocked}
            />
          </div>
        </div>
        {showBadges && (
          <span className={`${Style.badgeInfo} mt-11 mb-12`}>
            {type === 'collaboration' &&
            Array.isArray(item.projectCollaborator) &&
            item.projectCollaborator.length > 0
              ? `Owner: ${get(item, 'creator.username')}`
              : 'Collaborating'}
          </span>
        )}
      </div>
    );
  }
}

Cover.defaultProps = { callout: false, isCollaboratingProject: false };

Cover.propTypes = {
  t: PropTypes.func.isRequired,
  cover: PropTypes.object.isRequired,
  item: PropTypes.object.isRequired,
  accessibleFeature: PropTypes.object.isRequired,
  setSubscriptionJourneyModal: PropTypes.func.isRequired,
  callout: PropTypes.bool,
  isCollaboratingProject: PropTypes.bool,
};

export default connect(null, { setSubscriptionJourneyModal })(
  withTranslation('common')(Cover),
);
