@import '../../../styles/variable.scss';
@import '../../../styles/mixins.scss';

.background_dashboard {
  min-height: 100vh;
  height: 100%;
  display: block;
  // padding-bottom: 30px;
  width: 100%;
}

/*Hide in Other Small Devices */

/* Landscape phones and portrait tablets */

.badgeInfo {
  color: #000;

/* Body/B4 */
  font-family: "MaisonNeue";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%; /* 18px */
  display: inline-flex;
  padding: 4px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 6px;
  border: 1px solid #DBDBCF;
  background: var(--light, #FFF);
}

@media (max-width: $breakpoint-md) {
  .background_dashboard {
    background-color: $white;
  }
  .existingAppContainer {
    text-align: -webkit-center;
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .newAppContainer {
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

/* Landscape tablets and medium desktops */
@media (min-width: $breakpoint-md) and (max-width: $breakpoint-xl) {
  .background_dashboard {
    background-color: $white;
  }
}

.manageAndCreateYo {
  font-size: 16px;
  font-weight: 500;
  line-height: 32px;
}

.headerText {
  color: $black-solid;
  font-family: 'robotoCondensed';
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 0.88px;
  line-height: 40px;
  text-align: center;
}

.backGround {
  background-color: $grey-light;
}

/*Hide in Other Small Devices */

/* Landscape phones and portrait tablets */

@media (max-width: $breakpoint-sm) {
  .existingAppContainer {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .newAppContainer {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .newAppSubContainer {
    max-height: 349px;
    width: auto !important;
    background-color: $light-white;
  }
  .plusContainer {
    margin-top: 52px;
    border: 2px solid;
    background-color: $white;
    border-radius: 106px;
    max-width: 191px;
    margin-left: 0px !important;
    margin-right: 0px !important;
  }
}

@media (max-width: $breakpoint-lg) {
  .existingAppContainer {
    text-align: center !important;
  }
  .newAppSubContainer {
    // border: 2px solid $black-solid;
    max-height: 349px;
    width: auto !important;
    background-color: $light-white;
  }
  .plusContainer {
    margin-top: 52px;
    border: 2px solid;
    background-color: $white;
    border-radius: 106px;
    max-width: 191px;
    margin-left: 0px !important;
    margin-right: 0px !important;
  }
}

.lockIconContainer {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

.newAppContainer {
  border: 1px solid #dbdbcf;
  border-radius: 6px;
  width: 100%;
  background-color: #f2f0e4;
  text-align: -webkit-center;
  max-height: 475px;
  padding-bottom: 30px;
}

.gridContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
  margin-bottom: 32px;
}

.gridContainer > * {
  flex: 0 0 calc((100% - 32px) / 3);
  max-width: calc((100% - 32px) / 3);
  box-sizing: border-box;
}

/* Tablet responsiveness */
@media (min-width: 768px) and (max-width: 991px) {
  .gridContainer {
    justify-content: flex-start;
  }

  .gridContainer > * {
    flex: 0 0 calc((100% - 16px) / 2);
    max-width: calc((100% - 16px) / 2);
  }
}

/* Mobile responsiveness */
@media (max-width: 767px) {
  .gridContainer {
    justify-content: center;
  }

  .gridContainer > * {
    flex: 0 0 100%;
    max-width: 455px;
  }
}

/* For screen width between 992px and 1199px */
@media (min-width: 992px) and (max-width: 1199px) {
  .newAppContainer {
    max-width: 455px;
  }
}

/* For screen width between 768px and 991px */
@media (min-width: 768px) and (max-width: 991px) {
  .newAppContainer {
    max-width: 455px;
  }
}

/* For screen width less than 768px */
@media (max-width: 767px) {
  .newAppContainer {
    max-width: 455px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

/* Firefox-specific styling */
@-moz-document url-prefix() {
  .newAppContainer {
    text-align: -moz-center;
  }
}

.existingAppContainer {
  border: 1px solid #dbdbcf;
  border-radius: 6px;
  width: 100%;
  background-color: #f2f0e4;
  text-align: -webkit-center;
  padding-bottom: 30px;
  position: relative;
}

/* For screen width less than 768px */
@media (max-width: 767px) {
  .existingAppContainer {
    max-width: 455px;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

.selectedProject {
  border: 2px solid #05012d;
}
/* Firefox-specific styling */
@-moz-document url-prefix() {
  .existingAppContainer {
    text-align: -moz-center;
  }
}

.lockIconDiv {
  margin: -4px 0px 0px 5px;
}

.newAppSubContainer {
  // width: 100%;
  max-width: 382px;
  background-color: transparent;
}

.projects {
  color: $black-solid;
}

.existingAppSubContainer {
  border: 1px solid #dbdbcf;
  border-radius: 6px;
  background-color: $desc-bg;
  padding: 20px;
  // max-width: 280px;
  margin: 0 auto;
}

.collaboratorProjectOwnerContainer {
  position: relative;
  top: -16px;
}

.collaboratorProjectOwnerContainer:not(:first-child) {
  left: -20px;
}

.plusBorder {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.plusContainer {
  margin: 52px auto 0;
  border: 2px solid;
  background-color: $white;
  border-radius: 50%;
  width: 190px;
  height: 190px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 190px;
  min-height: 190px;
  aspect-ratio: 1/1;
}

.container {
  height: 100%;
  border: 3px solid $white;
  bottom: 10px;
  cursor: pointer;
}

.headers {
  font-size: 8px;
  font-weight: 500;
  word-wrap: break-word;
  color: $left-container-bg;
  font-family: 'robotoCondensed';
  margin-bottom: 0 !important;
}

.subHeader {
  color: $white;
  font-size: 12px;
  margin-left: 5px;
  font-family: 'robotoCondensed';
  word-wrap: break-word;
  text-transform: uppercase;
}

.vl {
  border: 2px solid $border-color;
  min-height: 45px;
}

.hr {
  border: 2px solid $border-color;
  box-sizing: border-box;
}

.hr:before {
  border: 2px solid $border-color;
  box-sizing: border-box;
  position: absolute;
}

.hr:after {
  border: 2px solid $border-color;
  box-sizing: border-box;
  position: relative;
}

.decisionMakerHeadertext {
  color: $theme-black-solid;
  font-size: 18px;
  letter-spacing: 0;
  line-height: 24px;
}

.director {
  justify-content: center;
  height: auto;
}

.writer {
  justify-content: center;
  height: auto;
}

.title {
  color: $white;
  font-size: 20px;
  font-weight: bold;
  white-space: initial;
  word-wrap: break-word;
  letter-spacing: 1px;
  line-height: 30px;
  text-transform: uppercase;
  margin-top: 24px;
  font-family: 'PettingillCFBold';
}

.titleContainer {
  padding-top: 40px;
  padding-bottom: 50px;
}

.existingTitle {
  text-align: center;
}

.iconPosition {
  position: absolute;
  top: 12px;
  right: 16px;
  width: 20px;
  z-index: 1;
  cursor: pointer;
}

.crossPosition {
  position: absolute;
  top: 10px;
  right: 15px;
  width: 20px;
  z-index: 1;
  cursor: pointer;
}

.dropDownContent {
  z-index: 1;
  padding: 0;
  top: 40px;
  right: 10px;
  position: absolute;
  background-color: $white;
  box-shadow: $shadow-4;
  padding: 5px;
  width: 148px;
}

.dropDownOptions {
  cursor: pointer;
  padding: 16px 24px;

  &:hover {
    background: #ecece0;
  }
}

.collaboratorDropDownOptions {
  cursor: pointer;
  // padding: 16px 15px;
  padding: 16px;

  &:hover {
    background: #ecece0;
  }
}

.modalTitle {
  text-align: center;
  font-size: 20px;
  line-height: 24px;
  color: #05012d;
}

.inviteInputHeading {
  font-family: 'maisonNeue';
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
  color: #05012d;
}

.inviteBtnText {
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  align-items: center;
  text-align: center;
  color: #ffffff;
  background-color: #05012d;
}

.removeGoalBtnText {
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  align-items: center;
  text-align: center;
  color: #05012d;
}

.inviteModal > div > div {
  background-color: #ecece0;
}

.pendingText {
  width: 80px;
  height: 40px;
  display: none;
  background-color: white;
}

.pendingName:hover {
  .pendingText {
    width: 80px;
    height: 35px;
    display: block;
    padding: 8px;
    background-color: white;
    margin: 0px;
  }
}

.creatorNameText {
  display: none;
  position: absolute;
  top: 5px;
  left: 30px;
  z-index: 999999;
  background: #ffffff;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.15);
}
.creatorDp {
  position: relative;
}

.creatorDp:hover {
  .creatorNameText {
    height: 28px;
    display: block !important;
    background: #ffffff;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.15);
  }
}

.greyBtn {
  background-color: #ecece0;
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 21px;
  text-align: center;
  padding: 4px 8px;
  text-transform: uppercase;
  border: 0px;
}

.footer {
  position: fixed;
  bottom: 0px;
}

.collaboratorsModalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collaboratorsModal {
  background: #ecece0;
  border-radius: 12px;
  min-width: 480px;
  min-height: 160px;
  max-width: 600px;
  padding: 40px 48px 32px 48px;
  box-shadow: 0 8px 48px rgba(0, 0, 0, 0.18);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.collaboratorsModalHeader {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
}

.collaboratorsModalTitle {
  font-size: 26px;
  font-weight: bold;
  color: #19194b;
  letter-spacing: 1px;
}

.collaboratorsModalClose {
  font-size: 28px;
  font-weight: bold;
  color: #19194b;
  cursor: pointer;
}

.collaboratorsModalBody {
  width: 100%;
  margin-top: 8px;
}

.collaboratorRow {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 18px;
  color: #19194b;
}

.ownerBadge {
  background: #fff;
  color: #19194b;
  font-size: 13px;
  border-radius: 4px;
  padding: 2px 10px;
  margin-left: 12px;
  border: 1px solid #d3d3c3;
}

.collaboratorName {
  color: var(--navy, #05012d);
  font-family: 'MaisonNeue';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.cardContainer {
  background: $white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 24px;
  border: 1px solid #eaeaea;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.2s;
  position: relative;
}

.cardContainer:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.projectMenuDropdown {
  z-index: 1;
  top: 40px;
  right: 10px;
  position: absolute;
  background-color: $white;
  box-shadow: $shadow-4;
  padding: 8px !important;
}
